<?php

declare(strict_types=1);

use App\Models\ProductOffer;
use App\Models\User;
use App\Modules\ShoppingList\Models\ClinicShoppingList;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('shopping_list_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignIdFor(ClinicShoppingList::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(ProductOffer::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(User::class, 'created_by_user_id')->constrained('users')->cascadeOnDelete();
            $table->unsignedInteger('quantity');
            $table->string('color')->nullable();
            $table->string('label')->nullable();
            $table->unsignedInteger('rank')->default(0);
            $table->timestamps();

            $table->unique(['clinic_shopping_list_id', 'product_offer_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('shopping_list_items');
    }
};
