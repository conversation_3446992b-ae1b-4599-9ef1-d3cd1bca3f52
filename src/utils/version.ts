/**
 * Get version from browser environment variables (runtime)
 * Priority: VITE_GIT_TAG > VITE_GIT_COMMIT
 */
const getVersionFromBrowserEnv = (): string | null => {
  // Try VITE_GIT_TAG first
  const gitTag = import.meta.env.VITE_GIT_TAG;
  if (gitTag) {
    return gitTag;
  }

  // Try VITE_GIT_COMMIT
  const gitCommit = import.meta.env.VITE_GIT_COMMIT?.substring(0, 7);
  if (gitCommit) {
    return gitCommit;
  }

  return null;
};

/**
 * Get version for runtime (browser environment)
 * Uses __APP_VERSION__ global variable set by Vite
 */
export const getRuntimeVersion = (): string => {
  // 1. Try __APP_VERSION__ (set by Vite build)
  const appVersion = (globalThis as Record<string, unknown>)
    .__APP_VERSION__ as string;
  if (appVersion && appVersion !== '""' && appVersion.trim() !== '') {
    return appVersion;
  }

  // 2. Try browser environment variables (for development)
  const envVersion = getVersionFromBrowserEnv();
  if (envVersion) {
    return envVersion;
  }

  return 'unknown';
};
