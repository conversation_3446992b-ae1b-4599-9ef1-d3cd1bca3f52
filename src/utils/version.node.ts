import { execSync } from 'child_process';

/**
 * Get version from Node.js environment variables (build-time)
 * Priority: VITE_GIT_TAG > VITE_GIT_COMMIT
 */
const getVersionFromNodeEnv = (): string | null => {
  // Try VITE_GIT_TAG first
  const gitTag = process.env.VITE_GIT_TAG?.trim();
  if (gitTag) {
    return gitTag;
  }

  // Try VITE_GIT_COMMIT
  const gitCommit = process.env.VITE_GIT_COMMIT?.substring(0, 7);
  if (gitCommit) {
    return gitCommit;
  }

  return null;
};

/**
 * Get git tag from local repository
 */
const getLocalGitTag = (): string | null => {
  try {
    const localGitTag = execSync('git describe --tags --abbrev=0', {
      encoding: 'utf8',
    }).trim();
    return localGitTag || null;
  } catch {
    return null;
  }
};

/**
 * Get version from multiple sources with fallback chain (Node.js build-time)
 * Priority: VITE_GIT_TAG > VITE_GIT_COMMIT > git tag
 */
export const getVersion = (): string => {
  // 1. Try Node.js environment variables
  const envVersion = getVersionFromNodeEnv();
  if (envVersion) {
    return envVersion;
  }

  // 2. Try local git tag
  const gitTag = getLocalGitTag();
  if (gitTag) {
    return gitTag;
  }

  // 3. No valid version found
  return 'unknown';
};
