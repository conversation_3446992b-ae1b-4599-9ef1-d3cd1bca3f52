.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  label {
    font-size: 0.875rem;
    line-height: 20px;
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  select,
  input {
    box-sizing: border-box;
    height: 2.5rem;
    padding: 0 0.75rem;
    border-radius: 0.25rem;
    background-color: #fff;
    border: 1px solid #eaecf0;
    width: 100%;
    font-size: 0.875rem;
    line-height: 1.4;

    &:focus-visible {
      outline: 2px solid #f9d763;
      outline-offset: 0.125rem;
    }

    &:disabled {
      background-color: #f2f2f2;
    }
  }

  .select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url('assets/select-arrow.svg');
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
  }

  .lg {
    height: 48px;
  }

  .small {
    height: 32px;
  }

  .error {
    font-size: 0.75rem;
    color: #920101;
    margin: 0.4rem 0 0;
    position: relative;
    max-width: 100%;
  }

  .rightSection {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
  }
}
