import { captureException } from '../sentry';

export interface ApiError {
  apiResponse?: Response;
  data?: unknown;
  message?: string;
}

/**
 * Safe stringify that handles circular refs and truncates large results
 */
const safeStringify = (value: unknown, maxLength: number = 1000): string => {
  if (value === null || value === undefined) {
    return String(value);
  }

  try {
    const stringified = JSON.stringify(value, null, 0);

    if (stringified.length <= maxLength) {
      return stringified;
    }

    return stringified.substring(0, maxLength - 3) + '...';
  } catch {
    // Handle circular references or other JSON errors
    return '[Unable to stringify]';
  }
};

/**
 * Sanitize context object to ensure only primitive values are passed to Sentry
 */
const sanitizeContext = (
  context?: Record<string, unknown>,
): Record<string, string | number | boolean> => {
  if (!context) return {};

  const sanitized: Record<string, string | number | boolean> = {};

  Object.entries(context).forEach(([key, value]) => {
    if (value === undefined || value === null) {
      return; // Skip undefined/null values
    }

    if (
      typeof value === 'string' ||
      typeof value === 'number' ||
      typeof value === 'boolean'
    ) {
      sanitized[key] = value;
    } else {
      // Convert complex values to strings using safe stringify
      sanitized[key] = safeStringify(value);
    }
  });

  return sanitized;
};

export const handleApiError = (
  error: unknown,
  context?: Record<string, unknown>,
  method?: string,
) => {
  const apiError = error as ApiError;
  const sanitizedContext = sanitizeContext(context);

  if (apiError?.apiResponse) {
    const status = apiError.apiResponse.status;
    const url = apiError.apiResponse.url;

    // Try to get method from parameter, context, or use default
    const httpMethod =
      method || (sanitizedContext.method as string) || 'UNKNOWN';

    const errorMessage = `API Error: ${status} - ${httpMethod}`;
    const errorToCapture =
      error instanceof Error ? error : new Error(errorMessage);

    captureException(errorToCapture, {
      ...sanitizedContext,
      status: status.toString(),
      url,
      method: httpMethod,
      apiError: 'true',
      apiErrorMessage: errorMessage,
      responseData: safeStringify(apiError.data),
    });
  } else if (error instanceof Error) {
    captureException(error, {
      ...sanitizedContext,
      apiError: 'false',
    });
  } else {
    captureException(new Error('Unknown API error'), {
      ...sanitizedContext,
      originalError: safeStringify(error),
      apiError: 'true',
    });
  }
};
