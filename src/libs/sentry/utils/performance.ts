import * as Sentry from '@sentry/react';

export const trackPerformance = <T>(
  name: string,
  operation: () => Promise<T>,
  attributes?: Record<string, string>,
): Promise<T> => {
  return Sentry.startSpan({ name, attributes }, async () => {
    return operation();
  });
};

export const trackMetric = (
  name: string,
  value: number,
  tags?: Record<string, string>,
) => {
  Sentry.addBreadcrumb({
    message: `Metric: ${name} = ${value}`,
    category: 'metric',
    level: 'info',
    data: { value, ...tags },
  });
};
