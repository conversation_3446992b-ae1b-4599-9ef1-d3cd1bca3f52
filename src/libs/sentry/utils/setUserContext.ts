import * as Sentry from '@sentry/react';
import { setUser } from '../sentry';

export const setUserContext = (user: {
  id: string;
  email: string;
  name?: string;
  role?: string;
  clinicId?: string;
}) => {
  setUser({
    id: user.id,
    email: user.email,
    username: user.name || user.email,
  });

  Sentry.setContext('user', {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    clinicId: user.clinicId,
  });

  // Always set or clear clinicId tag to prevent stale values
  if (user.clinicId) {
    Sentry.setTag('clinicId', user.clinicId);
  } else {
    // Clear the tag if clinicId is not present
    Sentry.setTag('clinicId', '');
  }

  // Always set or clear userRole tag to prevent stale values
  if (user.role) {
    Sentry.setTag('userRole', user.role);
  } else {
    // Clear the tag if role is not present
    Sentry.setTag('userRole', '');
  }
};
