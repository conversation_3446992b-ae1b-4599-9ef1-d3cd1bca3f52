import * as Sentry from '@sentry/react';
import { SentryConfig } from '../types';

export const initializeSentry = (config: SentryConfig) => {
  Sentry.init({
    dsn: config.dsn,
    environment: config.environment,
    release: config.release,
    tracesSampleRate: config.tracesSampleRate ?? 0.1,
    debug: config.debug || false,
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration({
        maskAllText: true,
        blockAllMedia: true,
        maskAllInputs: true,
      }),
    ],
    beforeSend(event) {
      if (event.exception) {
        const error = event.exception.values?.[0];
        if (error?.value?.includes('ResizeObserver loop limit exceeded')) {
          return null;
        }
        if (error?.value?.includes('Non-Error promise rejection')) {
          return null;
        }
        if (error?.value?.includes('Script error')) {
          return null;
        }
      }

      if (config.environment === 'development') {
        console.log('Sentry event:', event);
      }

      return event;
    },
  });

  if (config.appName) {
    Sentry.setTag('app', config.appName);
    Sentry.setContext('app', {
      name: config.appName,
      version: config.release || '1.0.0',
    });
  }
};
