import { SentryConfig, AppSentryConfig } from '../types';
import { getEnvironment } from './getEnvironment';
import { getRuntimeVersion } from '@/utils/version';

export const createSentryConfig = (
  appConfig: AppSentryConfig,
): SentryConfig => {
  const environment = appConfig.environment || getEnvironment();
  const isProduction = environment === 'production';

  return {
    dsn: appConfig.dsn || import.meta.env.VITE_SENTRY_DSN || '',
    environment,
    release:
      appConfig.release !== undefined ? appConfig.release : getRuntimeVersion(),
    tracesSampleRate:
      appConfig.tracesSampleRate ??
      (isProduction ? 0.1 : environment === 'development' ? 0.01 : 0.5),
    debug:
      appConfig.debug !== undefined
        ? appConfig.debug
        : environment === 'development',
    appName: appConfig.appName,
  };
};
