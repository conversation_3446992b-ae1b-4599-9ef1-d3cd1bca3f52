import { useEffect } from 'react';
import { setUserContext } from '../utils/setUserContext';
import { clearUser } from '../sentry';

interface User {
  id: string;
  email: string;
  name?: string;
  role?: string;
  clinicId?: string;
}

export const useSentryUser = (user: User | null) => {
  useEffect(() => {
    if (user) {
      setUserContext({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        clinicId: user.clinicId,
      });
    } else {
      clearUser();
    }
  }, [user]);
};
