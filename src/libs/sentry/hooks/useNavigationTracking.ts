import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { trackNavigation } from '../utils/navigationBreadcrumb';
import { trackPageView } from '../utils/navigationBreadcrumb';

export const useNavigationTracking = () => {
  const location = useLocation();

  useEffect(() => {
    trackNavigation(location.pathname, location.search);

    const pageName = location.pathname.split('/').pop() || 'home';
    trackPageView(pageName);
  }, [location]);
};
