import * as Sentry from '@sentry/react';

export const captureException = (
  error: Error,
  context?: Record<string, string | number | boolean>,
) => {
  Sentry.captureException(error, {
    tags: context,
  });
};

export const captureMessage = (
  message: string,
  level: 'info' | 'warning' | 'error' = 'info',
) => {
  Sentry.captureMessage(message, level);
};

export const addBreadcrumb = (
  message: string,
  category?: string,
  level?: 'info' | 'warning' | 'error',
) => {
  Sentry.addBreadcrumb({
    message,
    category: category || 'custom',
    level: level || 'info',
  });
};

export const setUser = (user: {
  id?: string;
  email?: string;
  username?: string;
}) => {
  Sentry.setUser(user);
};

export const setTag = (key: string, value: string) => {
  Sentry.setTag(key, value);
};

export const setContext = (
  key: string,
  context: Record<string, string | number | boolean>,
) => {
  Sentry.setContext(key, context);
};

export const clearUser = () => {
  Sentry.setUser(null);
};
