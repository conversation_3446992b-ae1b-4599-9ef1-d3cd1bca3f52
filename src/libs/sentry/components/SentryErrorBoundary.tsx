import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import * as Sentry from '@sentry/react';

import { ErrorSection } from '@/components';

interface SentryErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
}

export const SentryErrorBoundary: React.FC<SentryErrorBoundaryProps> = ({
  children,
  fallback: Fallback = ErrorSection,
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Send error to Sentry
    Sentry.captureException(error, {
      contexts: {
        react: {
          componentStack: errorInfo.componentStack || '',
        },
      },
    });
  };

  return (
    <ErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary }) => (
        <Fallback error={error} resetError={resetErrorBoundary} />
      )}
      onError={handleError}
      onReset={() => {
        // Clear Sentry scope on reset
        Sentry.getCurrentScope().clear();
      }}
    >
      {children}
    </ErrorBoundary>
  );
};
