import React from 'react';
import { captureException } from '../sentry';
import { captureMessage } from '../sentry';
import { addBreadcrumb } from '../sentry';

export const ErrorTestComponent: React.FC = () => {
  const triggerError = () => {
    try {
      throw new Error('Test error for Sentry');
    } catch (error) {
      captureException(error as Error, { component: 'ErrorTestComponent' });
    }
  };

  const triggerUnhandledRejection = () => {
    Promise.reject(new Error('Test unhandled promise rejection'));
  };

  const sendTestMessage = () => {
    captureMessage('Test message from ErrorTestComponent', 'info');
  };

  const addTestBreadcrumb = () => {
    addBreadcrumb('Test breadcrumb added', 'user-action', 'info');
  };

  return (
    <div className="rounded-lg border border-gray-300 bg-gray-50 p-4">
      <h3 className="mb-4 text-lg font-semibold">Sentry Error Testing</h3>
      <div className="space-y-2">
        <button
          onClick={triggerError}
          className="block w-full rounded bg-red-600 px-4 py-2 text-white transition-colors hover:bg-red-700"
        >
          Trigger Test Error
        </button>
        <button
          onClick={triggerUnhandledRejection}
          className="block w-full rounded bg-orange-600 px-4 py-2 text-white transition-colors hover:bg-orange-700"
        >
          Trigger Unhandled Rejection
        </button>
        <button
          onClick={sendTestMessage}
          className="block w-full rounded bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
        >
          Send Test Message
        </button>
        <button
          onClick={addTestBreadcrumb}
          className="block w-full rounded bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700"
        >
          Add Test Breadcrumb
        </button>
      </div>
    </div>
  );
};
