import ReactDOM from 'react-dom/client';
import '@/libs/i18n';
import { initializeSentry } from '@/libs/sentry/utils/initializeSentry';
import { createSentryConfig } from '@/libs/sentry/utils/createSentryConfig';
import { Shop } from './Shop';

initializeSentry(createSentryConfig({ appName: 'shop' }));

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement,
);
root.render(<Shop />);
