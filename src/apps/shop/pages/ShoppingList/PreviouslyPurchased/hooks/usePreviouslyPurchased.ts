import { useCallback, useEffect, useState } from 'react';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import { fetchPreviouslyPurchased } from '../services/fetchPreviouslyPurchased';
import {
  FetchPreviouslyPurchasedParams,
  PreviouslyPurchasedItemType,
} from '../../types';

type UsePreviouslyPurchasedState = {
  items: PreviouslyPurchasedItemType[];
  total: number;
  isLoading: boolean;
  errorOnLoading: boolean;
};

type UsePreviouslyPurchasedReturn = UsePreviouslyPurchasedState & {
  fetchItems: (params: FetchPreviouslyPurchasedParams) => void;
};

export const usePreviouslyPurchased = (
  initialParams: FetchPreviouslyPurchasedParams = {},
): UsePreviouslyPurchasedReturn => {
  const [state, setState] = useState<UsePreviouslyPurchasedState>({
    items: [],
    total: 0,
    isLoading: false,
    errorOnLoading: false,
  });

  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(initialParams.page || 1);
  const [perPage, setPerPage] = useState(initialParams.perPage || 12);

  const debouncedSearch = useDebounce(searchQuery, 300);

  const fetchItems = useCallback((params: FetchPreviouslyPurchasedParams) => {
    if (params.search !== undefined) setSearchQuery(params.search || '');
    if (params.page !== undefined) setPage(params.page);
    if (params.perPage !== undefined) setPerPage(params.perPage);
  }, []);

  useEffect(() => {
    if (debouncedSearch === '' || debouncedSearch.length >= 3) {
      const params = {
        search: debouncedSearch || undefined,
        page,
        perPage,
      };

      fetchPreviouslyPurchased({
        params,
        beforeStart: () => {
          setState((prev) => ({
            ...prev,
            isLoading: true,
            errorOnLoading: false,
          }));
        },
        onSuccess: (data) => {
          setState((prev) => ({
            ...prev,
            items: data.items,
            total: data.total,
          }));
        },
        onError: () => {
          setState((prev) => ({ ...prev, errorOnLoading: true }));
        },
        afterDone: () => {
          setState((prev) => ({ ...prev, isLoading: false }));
        },
      });
    }
  }, [debouncedSearch, page, perPage]);

  useEffect(() => {
    fetchItems(initialParams);
  }, [fetchItems, initialParams]);

  return {
    ...state,
    fetchItems,
  };
};
