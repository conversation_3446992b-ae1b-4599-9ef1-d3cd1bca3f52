import { useState } from 'react';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { Link } from 'react-router-dom';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { getPriceString, mergeClasses } from '@/utils';
import { ItemActions } from '../../../components/ItemActions/ItemActions';
import { OfferType } from '@/types';
import { PreviouslyPurchasedItemType } from '../../../types';

type PreviouslyPurchasedItemProps = {
  className?: string;
  item: PreviouslyPurchasedItemType;
};

export const PreviouslyPurchasedItem = ({
  className,
  item,
}: PreviouslyPurchasedItemProps) => {
  const { product } = item;
  const productOffer = product.offers.find(
    (offer) => item.productOfferId === offer.id,
  ) as OfferType;

  const [currentOffer, setCurrentOffer] = useState(productOffer);

  const productUrl = getProductUrl(product.id, currentOffer.id);

  const handleSwapVendor = (newOfferId: string) => {
    const newOffer = product.offers.find(({ id }) => newOfferId === id);
    if (newOffer) setCurrentOffer(newOffer);
  };

  const { salePrice, originalPrice } =
    getProductOfferComputedData(currentOffer);

  return (
    <div
      className={mergeClasses(
        'flex w-full justify-between rounded-lg border border-black/[0.06] bg-white p-4 pr-2',
        className,
      )}
    >
      <div className="flex flex-col justify-center gap-1">
        <Link
          to={productUrl}
          className="mr-4 max-w-md text-sm font-semibold no-underline hover:underline"
        >
          {product.name}
        </Link>

        <div className="mr-auto flex">
          <span className="text-xs font-semibold">
            {currentOffer.vendor.name}
          </span>
          <div className="divider-v"></div>
          <span className="text-xs text-black/50">
            SKU: <span className="text-black">{currentOffer.vendorSku}</span>
          </span>
          <div className="divider-v"></div>
          <span className="text-xs text-black/50">
            Per unit:{' '}
            {originalPrice > salePrice ? (
              <>
                <span className="mx-1 text-xs font-semibold text-black">
                  {getPriceString(salePrice)}
                </span>
                <span className="text-sxs line-through">
                  {getPriceString(originalPrice)}
                </span>
              </>
            ) : (
              <span className="mx-1 text-xs font-semibold text-black">
                {getPriceString(salePrice)}
              </span>
            )}{' '}
          </span>
        </div>
      </div>

      <ItemActions
        product={product}
        originalPrice={originalPrice}
        salePrice={salePrice}
        currentOffer={currentOffer}
        onSwap={handleSwapVendor}
        lastOrderedQuantity={item.lastOrderedQuantity}
      />
    </div>
  );
};
