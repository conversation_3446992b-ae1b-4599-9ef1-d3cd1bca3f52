import { getPriceString } from '@/utils';
import { SHIPPING_OPTIONS_VALUES } from '../../../ShippingFeeModal/constants';

interface ShippingTerms {
  defaultShippingFee: string;
  freeShippingThreshold: string;
  isClinicSpecific: boolean;
  shippingRate: string;
  shippingType: string | null;
}

interface ShippingFeeInfoProps {
  shippingTerms: ShippingTerms;
}

export const ShippingFeeInfo = ({ shippingTerms }: ShippingFeeInfoProps) => {
  const getShippingTypeLabel = (shippingType: string | null) => {
    switch (shippingType) {
      case 'FREE_OVER_MINIMUM':
        return 'Free Shipping Over minimum';
      case 'FLAT_FEE':
        return 'Flat Fee per Order';
      case 'ALWAYS_FREE':
        return 'Free Shipping on all orders';
      default:
        return 'Free Shipping Over minimum';
    }
  };

  const formatCurrency = (value: string) => {
    const numericValue = parseFloat(value);
    return getPriceString(numericValue);
  };

  const shippingType =
    shippingTerms.shippingType || SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM;
  const showFreeShippingThreshold =
    shippingType === SHIPPING_OPTIONS_VALUES.FREE_OVER_MINIMUM;

  return (
    <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
      <div className="space-y-3">
        <div>
          <p className="lh-1 text-xs text-[#666]">Shipping Fee Type</p>
          <p className="lh-1 text-xs font-medium text-[#333]">
            {getShippingTypeLabel(shippingTerms.shippingType)}
          </p>
        </div>

        <div className="border-t border-gray-200 pt-3">
          <div className="flex items-center justify-between">
            <div className="flex w-[35%] gap-1">
              <p className="lh-1 text-xs text-[#666]">Fee:</p>
              <p className="lh-1 text-xs font-medium text-[#333]">
                {formatCurrency(shippingTerms.defaultShippingFee)}
              </p>
            </div>

            {showFreeShippingThreshold && (
              <div className="flex w-[65%] gap-1 border-l border-gray-200 pl-4">
                <p className="lh-1 text-xs text-[#666]">Free Threshold:</p>
                <p className="lh-1 text-xs font-medium text-[#333]">
                  {formatCurrency(shippingTerms.freeShippingThreshold)}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
