import { Loader } from '@/libs/ui/Loader/Loader';
import { Flex } from '@/libs/ui/Flex/Flex';
import { Suspense, useEffect } from 'react';
import { Navigate, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { SentryErrorBoundary } from '@/libs/sentry/components/SentryErrorBoundary';
import { ProtectedLayout } from '@/apps/shop/Layouts/ProtectedLayout/ProtectedLayout';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { CLINIC_NAV_LINKS, CLINIC_SETTING_LINKS } from '@/apps/shop/constants';
import { useMoniteNavigation } from '@/libs/monite/hooks/useMoniteAccess';
import { FEATURE_FLAGS } from '@/constants';

import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { getActiveClinic } from '@/libs/clinics/utils/activeClinic';
import { MainSidebar } from '../../components/MainSidebar/MainSidebar';
import { ShopTopNavbar } from '../../components/ShopTopNavbar/ShopTopNavbar';
import { AppContentWrap } from '@/libs/ui/AppContentWrap/AppContentWrap';

interface ClinicLayoutProps {
  showCart?: boolean;
}
export const ClinicLayout = ({ showCart }: ClinicLayoutProps) => {
  const { getClinic, clinic } = useClinicStore();
  const { fetchCart } = useCartStore();
  const { activeClinic } = useAccountStore();
  const location = useLocation();
  const navigate = useNavigate();
  const localStorageActiveClinic = getActiveClinic();

  // Check Monite access for navigation filtering
  const { showInvoicesMenuItem } = useMoniteNavigation(activeClinic?.id);

  useEffect(() => {
    if (activeClinic && !activeClinic.hasAnyVendorConnected) {
      navigate(SHOP_ROUTES_PATH.vendors);
    }
  }, [location.pathname, navigate, clinic, activeClinic]);

  const loadInitialUserData = async () => {
    if (localStorageActiveClinic) {
      await getClinic(localStorageActiveClinic.id);
    }
  };

  useEffect(() => {
    if (localStorageActiveClinic) {
      fetchCart();
    }
  }, []);

  // Filter navigation links based on feature flags
  const filteredClinicNavLinks = CLINIC_NAV_LINKS.filter((link) => {
    // Hide invoices menu item if Monite feature is disabled
    if (link.path === SHOP_ROUTES_PATH.invoices) {
      return showInvoicesMenuItem;
    }
    if (link.path === SHOP_ROUTES_PATH.shoppingList) {
      return FEATURE_FLAGS.SHOPPING_LIST;
    }
    return true;
  });

  const navLinkGroups = [
    {
      title: 'Main menu',
      links: filteredClinicNavLinks,
    },
    {
      title: 'Preferences',
      links: CLINIC_SETTING_LINKS,
    },
  ];

  if (!localStorageActiveClinic) {
    return <Navigate to={SHOP_ROUTES_PATH.home} />;
  }

  return (
    <SentryErrorBoundary>
      <Suspense
        fallback={
          <div className="loaderRoot height100vh">
            <Loader size="3rem" />
          </div>
        }
      >
        <ProtectedLayout additionApiCall={loadInitialUserData}>
          <AppContentWrap>
            <Flex>
              <MainSidebar navLinkGroups={navLinkGroups} />
              <Flex direction="column" w="100%">
                <ShopTopNavbar showCart={showCart} />
                <SentryErrorBoundary>
                  <Outlet />
                </SentryErrorBoundary>
              </Flex>
            </Flex>
          </AppContentWrap>
        </ProtectedLayout>
      </Suspense>
    </SentryErrorBoundary>
  );
};
