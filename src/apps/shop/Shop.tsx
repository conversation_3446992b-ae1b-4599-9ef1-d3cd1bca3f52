import React from 'react';
import { RouterProvider } from 'react-router-dom';
import { LoadingOverlay, Portal } from '@mantine/core';
import { Notifications } from '@mantine/notifications';

import { ThemeProvider } from '@/providers/ThemeProvider';
import { QueryClientProvider } from '@tanstack/react-query';
import { useBoxLoading } from '@/apps/shop/stores/useBoxLoadingStore';
import { queryClient } from '@/libs/query/queryClient';
import router from './routes';
import EnvironmentBanner from '@/components/EnvironmentBanner';

import '@mantine/dates/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/dropzone/styles.css';
import '@mantine/core/styles.css';
import '@/assets/css/index.css';
import '@/assets/css/tailwind.css';

export const Shop = () => {
  const { isBoxLoading, boxId } = useBoxLoading();

  return (
    <ThemeProvider>
      <QueryClientProvider client={queryClient}>
        <EnvironmentBanner />
        <RouterProvider router={router} />
        <Notifications position="top-right" zIndex={1000} />

        {isBoxLoading && (
          <Portal target={boxId}>
            <LoadingOverlay visible />
          </Portal>
        )}
      </QueryClientProvider>
    </ThemeProvider>
  );
};
