import { GpoVendorData } from '../../types';
import { getPriceString, formatCompactNumber, mergeClasses } from '@/utils';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { Badge } from '@/libs/ui/Badge/Badge';
import { ProgressBar } from '@/libs/ui/ProgressBar/ProgressBar';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { MODAL_NAME } from '@/constants';
import { Menu, MenuItem } from '@/libs/ui/Menu/Menu';
import { AccountSettings } from '../../hooks/useAccountSettings';
import { Tooltip } from '@/libs/ui/Tooltip';

interface VendorCardProps {
  vendorData: GpoVendorData;
  totalSpend: number;
  accountSettings: AccountSettings[];
}

export const VendorCard = ({
  vendorData,
  totalSpend,
  accountSettings,
}: VendorCardProps) => {
  const { openModal } = useModalStore();

  const formatPercentage = (value: number) => {
    return `${value}%`;
  };

  // Check if vendor already has a goal defined
  const vendorGoalsSetting = accountSettings.find(
    (setting) => setting.setting_key === 'vendor_goals',
  );
  const existingVendorGoal = vendorGoalsSetting?.value.goals.find(
    (goal) => goal.vendor_id === vendorData.id,
  );
  const hasExistingGoal = !!existingVendorGoal;

  const calculateProgressPercentage = () => {
    const vendorTotalSpend = +vendorData.totalSpend;
    const goalAmount = +vendorData.goalAmount;

    return Math.min(vendorTotalSpend / goalAmount, 1);
  };

  const hasGoalSet = vendorData.goalAmount > 0;
  const isGoalReached = hasGoalSet && vendorData.amountUntilGoal <= 0;
  const progressPercentage = calculateProgressPercentage();

  return (
    <div className="rounded-xl border border-[#F5F5F5] bg-white p-5">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex w-[268px] items-center gap-3">
          <div className="flex h-12 flex-col items-center justify-center rounded-lg text-center">
            <img
              src={vendorData.imageUrl}
              alt={vendorData.name}
              className="h-full w-auto"
            />
          </div>
          <div className="flex flex-col gap-[0.3rem]">
            <h4 className="text-[1rem] font-medium text-[#344054]">
              {vendorData.name}
            </h4>
            <p className="text-[10px] font-bold text-[#666] capitalize">
              {vendorData.type}
            </p>
          </div>
        </div>

        <div className="divider-v h-8" />

        <div className="flex flex-1 items-center justify-between">
          <div className="flex min-w-[190px] flex-col px-8">
            <p className="mb-1 text-xs text-[#666]">Market Share %</p>
            <div className="flex items-center gap-2">
              <p className="text-sm font-semibold text-[#344054]">
                {formatPercentage(vendorData.marketSharePercentage)}
                <span className="ml-1 text-xs text-[#344054] opacity-60">
                  ({formatCompactNumber(vendorData.totalSpend)} from{' '}
                  {formatCompactNumber(totalSpend)} )
                </span>
              </p>
            </div>
          </div>
          <div className="divider-v h-8" />
          <div className="flex min-w-[190px] flex-col px-8">
            <p className="mb-1 text-xs text-[#666]">Total Spend</p>
            <div className="flex items-center gap-2">
              <p className="text-sm font-semibold text-[#344054]">
                {getPriceString(vendorData.totalSpend)}
              </p>
            </div>
          </div>
          <div className="divider-v h-8" />
          <div className="flex min-w-[190px] flex-col px-8">
            <p className="mb-1 text-xs text-[#666]">Growth Target %</p>
            <div className="flex items-center gap-2">
              <p className="text-sm font-semibold text-[#344054]">
                {formatPercentage(vendorData.growthTargetPercentage)}
                <span className="ml-1 text-xs text-[#344054] opacity-60">
                  ({formatCompactNumber(vendorData.totalSpend)} from{' '}
                  {formatCompactNumber(vendorData.goalAmount)} )
                </span>
              </p>
            </div>
          </div>
        </div>

        <div className="flex w-[80px] items-center justify-end gap-2">
          {/* TODO: Add download button */}
          {/*
          <Button
            variant="white"
            className="max-w-[60px]"
            aria-label="Download"
            onClick={() => {
              console.log('WIP...');
            }}
          >
            <Icon name="download" aria-hidden={true} />
          </Button> */}
          <Menu
            trigger={
              <Button
                variant="unstyled"
                className="max-w-[60px]"
                aria-label="More options"
              >
                <Icon name="moreOptions" aria-hidden={true} />
              </Button>
            }
            side="bottom"
            align="end"
          >
            <MenuItem
              onClick={() =>
                openModal({
                  name: MODAL_NAME.VENDOR_SETTINGS,
                  vendor: vendorData,
                })
              }
            >
              {hasExistingGoal ? 'Update vendor goal' : 'Set vendor goal'}
            </MenuItem>
          </Menu>
        </div>
      </div>

      <div className="divider-h w-full" />

      <div className="mt-4 flex items-center rounded-full bg-[#F2F2F2] px-4 py-[14px]">
        <div className="flex w-[268px] items-center justify-between pl-4">
          <p className="text-xs font-normal text-[#98A2B3]">
            {isGoalReached ? (
              <span className="text-sm font-bold text-[#333]">
                Goal Reached 🎉
              </span>
            ) : hasGoalSet ? (
              <>
                <span className="text-sm font-bold text-[#333]">
                  {getPriceString(vendorData.amountUntilGoal)}{' '}
                </span>
                until vendor goal
              </>
            ) : (
              <Button
                variant="unstyled"
                onClick={() =>
                  openModal({
                    name: MODAL_NAME.VENDOR_SETTINGS,
                    vendor: vendorData,
                  })
                }
              >
                <span className="text-sm font-medium text-[#518ef8] underline">
                  {hasExistingGoal ? 'Update vendor goal' : 'Set vendor goal'}
                </span>
              </Button>
            )}
          </p>
        </div>
        <div className="divider-v h-8" />
        <div className="flex flex-1 items-center justify-between pr-4">
          <Badge
            className={mergeClasses(
              'h-[20px] w-[68px] p-0 text-xs font-bold text-white',
              {
                ['bg-[#518ef8]']: hasGoalSet,
                ['bg-[rgba(82,82,82,0.4)]']: !hasGoalSet,
              },
            )}
          >
            Start
          </Badge>
          <div className="flex-1 px-2 pt-4">
            {hasGoalSet ? (
              <ProgressBar
                showLegend={false}
                values={[
                  {
                    value: progressPercentage * 100,
                    color: '#518ef8',
                  },
                  {
                    value: 100 - progressPercentage * 100,
                    color: '#EEE9FC',
                  },
                ]}
              />
            ) : (
              <ProgressBar
                showLegend={false}
                values={[
                  {
                    value: 100,
                    color: '#EEE9FC',
                  },
                ]}
              />
            )}
          </div>
          <Tooltip
            label={
              hasExistingGoal
                ? `Goal amount: ${getPriceString(vendorData.goalAmount)}`
                : ''
            }
          >
            <Badge
              className={mergeClasses(
                'h-[20px] w-[68px] p-0 text-xs font-bold text-white',
                {
                  ['bg-[#518ef8]']: isGoalReached,
                  ['bg-[rgba(82,82,82,0.4)]']: !isGoalReached,
                },
              )}
            >
              Goal
            </Badge>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};
