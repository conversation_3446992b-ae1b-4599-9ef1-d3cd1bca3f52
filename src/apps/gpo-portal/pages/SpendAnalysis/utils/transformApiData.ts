import { GpoClinicData } from '../types';
import {
  ClinicData,
  SpendAnalysisData,
} from '../components/ClinicCard/ClinicCard';

export const transformApiDataToClinicCard = (
  apiData: GpoClinicData,
): {
  clinic: ClinicData;
  spendAnalysis: SpendAnalysisData;
} => {
  try {
    if (!apiData) {
      console.warn(
        'transformApiDataToClinicCard: apiData is null or undefined',
      );
      throw new Error('API data is null or undefined');
    }

    if (!apiData.market_share_analysis?.product_categories) {
      console.warn(
        'transformApiDataToClinicCard: product_categories is missing',
        apiData.market_share_analysis,
      );
    }
    const clinic: ClinicData = {
      id: apiData.id || '',
      name: apiData.name || 'Unknown Clinic',
      ein: '',
      email: '',
      phoneNumber: '',
      isActive: apiData.status === 'ACTIVE',
      memberSince: apiData.member_since || '',
      shippingAddress: {
        addressId: '',
        street: '',
        state: '',
        city: '',
        postalCode: '',
      },
      practiceType: (apiData.practice_types || []).join(', '),
      fulltimeDvmCount: apiData.fulltime_dvm || 0,
      examRoomsCount: apiData.total_exam_rooms || 0,
    };

    const spendAnalysis: SpendAnalysisData = {
      totalSpend: apiData.total_spend?.amount || '0',
      rebateEarned: apiData.rebates_earned?.amount || '0',
      preferredShare: `${apiData.preferred_share?.percentage || 0}%`,
      totalSpendYoy: apiData.total_spend?.yoy_percentage || 0,
      rebateEarnedYoy: apiData.rebates_earned?.yoy_percentage || 0,
      preferredShareYoy: apiData.preferred_share?.yoy_percentage || 0,
      spendPercentage: `${apiData.spend?.preferred_vendor_percentage || 0}%`,
      spendAmount: apiData.spend?.total_spend || '0',
      spendYoyPercentage: apiData.total_spend?.yoy_percentage || 0,
      marketSharePercentage: `${apiData.market_share_analysis?.gpo_vendor_participation_rate?.percentage || 0}%`,
      vendorCount:
        apiData.market_share_analysis?.gpo_vendor_participation_rate
          ?.description || '0 out of 0',
      marketShareYoyPercentage:
        apiData.market_share_analysis?.gpo_vendor_participation_rate
          ?.yoy_percentage || 0,
      annualBudget: apiData.spend?.annual_budget || '0',
      prevYearSpend: apiData.spend?.previous_year_spend || '0',
      preferredVendorsPercentage:
        apiData.spend?.preferred_vendor_percentage || 0,
      nonPreferredVendorsPercentage:
        apiData.spend?.non_preferred_vendor_percentage || 0,
      distributorData:
        (apiData.market_share_analysis?.vendors || [])
          .find((vendor) => vendor.type === 'distributor')
          ?.items.map((item, index) => ({
            name: item.name || 'Unknown',
            percentage: item.percentage || 0,
            color: getColorByIndex(index),
          })) || [],
      manufacturerData:
        (apiData.market_share_analysis?.vendors || [])
          .find((vendor) => vendor.type === 'manufacturer')
          ?.items.filter((item) => item.percentage > 0)
          .map((item, index) => ({
            name: item.name || 'Unknown',
            percentage: item.percentage || 0,
            color: getColorByIndex(index),
          })) || [],
      productCategoriesData: {
        parasiticides:
          (apiData.market_share_analysis?.product_categories || [])
            .find(
              (cat) =>
                cat.name && cat.name.toLowerCase().includes('parasiticide'),
            )
            ?.vendors?.map((vendor, index) => ({
              name: vendor.name || 'Unknown',
              percentage: vendor.percentage || 0,
              color: getColorByIndex(index),
            })) || [],
        vaccines:
          (apiData.market_share_analysis?.product_categories || [])
            .find(
              (cat) => cat.name && cat.name.toLowerCase().includes('vaccine'),
            )
            ?.vendors?.map((vendor, index) => ({
              name: vendor.name || 'Unknown',
              percentage: vendor.percentage || 0,
              color: getColorByIndex(index),
            })) || [],
        diets:
          (apiData.market_share_analysis?.product_categories || [])
            .find((cat) => cat.name && cat.name.toLowerCase().includes('diet'))
            ?.vendors?.map((vendor, index) => ({
              name: vendor.name || 'Unknown',
              percentage: vendor.percentage || 0,
              color: getColorByIndex(index),
            })) || [],
      },
    };

    return { clinic, spendAnalysis };
  } catch (error) {
    console.error('Error transforming API data:', error);

    return {
      clinic: {
        id: apiData.id || 'error',
        name: apiData.name || 'Error Loading Clinic',
        ein: '',
        email: '',
        phoneNumber: '',
        isActive: false,
        memberSince: '',
        shippingAddress: {
          addressId: '',
          street: '',
          state: '',
          city: '',
          postalCode: '',
        },
        practiceType: '',
        fulltimeDvmCount: 0,
        examRoomsCount: 0,
      },
      spendAnalysis: {
        totalSpend: '0',
        rebateEarned: '0',
        preferredShare: '0%',
        totalSpendYoy: 0,
        rebateEarnedYoy: 0,
        preferredShareYoy: 0,
        spendPercentage: '0%',
        spendAmount: '0',
        spendYoyPercentage: 0,
        marketSharePercentage: '0%',
        vendorCount: '0 out of 0',
        marketShareYoyPercentage: 0,
        annualBudget: '0',
        prevYearSpend: '0',
        preferredVendorsPercentage: 0,
        nonPreferredVendorsPercentage: 0,
        distributorData: [],
        manufacturerData: [],
        productCategoriesData: {
          parasiticides: [],
          vaccines: [],
          diets: [],
        },
      },
    };
  }
};

const getColorByIndex = (index: number): string => {
  const colors = [
    '#4A90E2', // Blue
    '#F5A623', // Orange
    '#7ED321', // Green
    '#9B59B6', // Purple
    '#E67E22', // Dark Orange
    '#3498DB', // Dark Blue
    '#E74C3C', // Red
    '#2ECC71', // Light Green
    '#F39C12', // Light Orange
    '#8E44AD', // Dark Purple
  ];
  return colors[index % colors.length];
};
