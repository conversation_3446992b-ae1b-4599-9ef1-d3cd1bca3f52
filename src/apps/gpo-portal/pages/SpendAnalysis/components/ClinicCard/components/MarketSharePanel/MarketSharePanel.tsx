import { ReportPanel } from '../ReportPanel/ReportPanel';
import { ProgressBar } from '@/libs/ui/ProgressBar/ProgressBar';

interface MarketSharePanelProps {
  marketSharePercentage: string;
  vendorCount: string;
  yoyPercentage: number;
  // Additional dynamic data
  distributorData: Array<{
    name: string;
    percentage: number;
    color: string;
  }>;
  manufacturerData: Array<{
    name: string;
    percentage: number;
    color: string;
  }>;
  productCategoriesData: {
    parasiticides: Array<{
      name: string;
      percentage: number;
      color: string;
    }>;
    vaccines: Array<{
      name: string;
      percentage: number;
      color: string;
    }>;
    diets: Array<{
      name: string;
      percentage: number;
      color: string;
    }>;
  };
}

export const MarketSharePanel = ({
  // marketSharePercentage,
  // vendorCount,
  // yoyPercentage,
  distributorData,
  manufacturerData,
  productCategoriesData,
}: MarketSharePanelProps) => {
  const hasDistributorData = distributorData && distributorData.length > 0;
  const hasManufacturerData = manufacturerData && manufacturerData.length > 0;

  const hasProductCategoriesData =
    productCategoriesData &&
    (productCategoriesData.parasiticides?.length > 0 ||
      productCategoriesData.vaccines?.length > 0 ||
      productCategoriesData.diets?.length > 0);

  if (
    !hasDistributorData &&
    !hasManufacturerData &&
    !hasProductCategoriesData
  ) {
    return null;
  }

  return (
    <ReportPanel
      title="Market Share Analysis"
      label=""
      value=""
      subtitle=""
      percentage={0}
    >
      <div className="flex flex-col gap-2 px-4 py-3">
        {hasDistributorData && (
          <div className="flex gap-5 rounded bg-[#F2F2F2] px-4 py-6">
            <div className="w-[170px]">
              <p>Distributor</p>
            </div>
            <div className="w-[1px] bg-[#ddd]" />
            <div className="flex-1">
              <ProgressBar
                values={distributorData.map((item) => ({
                  value: item.percentage,
                  color: item.color,
                  label: item.name,
                }))}
              />
            </div>
          </div>
        )}
        {hasManufacturerData && (
          <div className="flex gap-5 rounded bg-[#F2F2F2] px-4 py-6">
            <div className="w-[170px]">
              <p>Manufacturer</p>
            </div>
            <div className="w-[1px] bg-[#ddd]" />
            <div className="flex-1">
              <ProgressBar
                values={manufacturerData.map((item) => ({
                  value: item.percentage,
                  color: item.color,
                  label: item.name,
                }))}
              />
            </div>
          </div>
        )}
        {hasProductCategoriesData && (
          <div className="flex gap-5 rounded bg-[#F2F2F2] px-4 py-6">
            <div className="w-[170px]">
              <p>Products Categories</p>
            </div>
            <div className="w-[1px] bg-[#ddd]" />
            <div className="flex flex-1 flex-col gap-2">
              {productCategoriesData.parasiticides?.length > 0 && (
                <>
                  <ProgressBar
                    title="Parasiticides"
                    values={productCategoriesData.parasiticides.map((item) => ({
                      value: item.percentage,
                      color: item.color,
                      label: item.name,
                    }))}
                  />
                  <div className="my-4 h-[1px] w-full bg-[#ddd]" />
                </>
              )}

              {productCategoriesData.vaccines?.length > 0 && (
                <>
                  <ProgressBar
                    title="Vaccines"
                    values={productCategoriesData.vaccines.map((item) => ({
                      value: item.percentage,
                      color: item.color,
                      label: item.name,
                    }))}
                  />
                  <div className="my-4 h-[1px] w-full bg-[#ddd]" />
                </>
              )}

              {productCategoriesData.diets?.length > 0 && (
                <ProgressBar
                  title="Diets"
                  values={productCategoriesData.diets.map((item) => ({
                    value: item.percentage,
                    color: item.color,
                    label: item.name,
                  }))}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </ReportPanel>
  );
};
