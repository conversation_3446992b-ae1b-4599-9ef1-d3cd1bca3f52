import { Checkbox } from '@/libs/form/Checkbox';
import { Input } from '@/libs/form/Input';
import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';
import { ClinicCard } from './components/ClinicCard/ClinicCard';
import { FilterControls } from './components/FilterControls/FilterControls';
import { NoResults } from '@/libs/ui/NoResults/NoResults';
import { SpendAnalysisLoader } from './components/SpendAnalysisLoader/SpendAnalysisLoader';
import { useState, useMemo, useEffect } from 'react';
import { useSpendAnalysis } from './hooks/useSpendAnalysis';
import { transformApiDataToClinicCard } from './utils/transformApiData';
import { exportSheet } from '@/libs/dashboard/utils/exportSheet';
import { FEATURE_FLAGS } from '@/constants';
import { Pagination } from '@/libs/ui/Pagination/Pagination';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import dayjs from 'dayjs';

export const SpendAnalysis = () => {
  const [selectedClinics, setSelectedClinics] = useState<Set<string>>(
    new Set(),
  );
  const [allSelected, setAllSelected] = useState(false);
  const [query, setQuery] = useState('');

  const { data, isLoading, error, filters, updateFilters, goToPage } =
    useSpendAnalysis({
      date_from: dayjs().startOf('year').format('YYYY-MM-DD'),
      date_to: dayjs().format('YYYY-MM-DD'),
      per_page: 12,
    });

  const debouncedUpdateFilters = useDebounce(query.trim(), 500);

  useEffect(() => {
    updateFilters({
      query: debouncedUpdateFilters,
    });
  }, [updateFilters, debouncedUpdateFilters]);

  const clinicList = useMemo(() => {
    if (!data?.data) return [];
    return data.data.map(transformApiDataToClinicCard);
  }, [data]);

  const handleSelectAll = (checked: boolean) => {
    setAllSelected(!!checked);
    if (!checked) {
      setSelectedClinics(new Set());
    }
  };

  const handleSelectClinic = (clinicId: string, checked: boolean) => {
    const newSelected = new Set(selectedClinics);
    if (checked) {
      newSelected.add(clinicId);
    } else {
      newSelected.delete(clinicId);
    }
    setSelectedClinics(newSelected);
  };

  const handleClinicsPerPageChange = (perPage: number) => {
    updateFilters({ per_page: perPage });
  };

  const handleInactiveClinicsToggle = (inactiveOnly: boolean) => {
    updateFilters({ inactive_only: inactiveOnly });
  };

  const handleSearchChange = (value: string) => {
    setQuery(value);
  };

  if (error) {
    throw new Error('Unexpected error');
  }

  return (
    <div className="bg-[#FAFAFA] px-[1.5rem] py-[2rem]">
      <h1 className="text-2xl font-medium">Spend Analysis Details</h1>
      <div className="mt-4 rounded border border-black/[0.04] bg-white p-6">
        <header className="mb-4 flex items-center justify-between">
          <h2 className="m-0 font-medium">
            Your Clinics ({data?.pagination?.total || 0})
          </h2>
          <div className="flex gap-3">
            <div className="min-w-[220px]">
              <Input
                placeholder="Search Clinics"
                value={query}
                onChange={(e) => handleSearchChange(e.target.value)}
                rightSection={
                  <Icon name="magnifier" size="16px" color="#C6C6C6" />
                }
              />
            </div>
            {FEATURE_FLAGS.SPEND_ANALYSIS_SORT && (
              <Button variant="white" p="0 1.5rem" onClick={() => {}}>
                <Icon name="sort" size="20px" />
                <span className="ml-1">Order by</span>
              </Button>
            )}
            {FEATURE_FLAGS.SPEND_ANALYSIS_FILTER && (
              <Button variant="white" p="0 1.5rem">
                <Icon name="settings" size="20px" />
                <span className="ml-1">Filter by</span>
              </Button>
            )}
            <Button
              variant="white"
              className="max-w-[56px]"
              onClick={() =>
                exportSheet({
                  path: '/gpo/spend-analysis/export',
                  filters: {
                    ...filters,
                    ...(allSelected
                      ? {
                          per_page: data?.pagination?.total,
                          page: 1,
                        }
                      : {}),
                    ...(selectedClinics.size > 0 && !allSelected
                      ? { clinic_ids: [...selectedClinics] }
                      : {}),
                  },
                  filename: 'spend-analysis',
                  format: 'xlsx',
                })
              }
            >
              <Icon name="download" size="16px" />
            </Button>
          </div>
        </header>
        <div className="rounded border border-black/[0.04] bg-[#F2F8FC] p-6">
          <div className="mb-4 flex items-center justify-between">
            <label className="flex gap-4 pl-4">
              <Checkbox
                checked={allSelected}
                onChange={(e) => handleSelectAll(e.target.checked)}
              />
              <p className="m-0">Select all clinics</p>
            </label>

            <FilterControls
              clinicsPerPage={filters.per_page || 25}
              onClinicsPerPageChange={handleClinicsPerPageChange}
              inactiveClinicsOnly={filters.inactive_only || false}
              onInactiveClinicsToggle={handleInactiveClinicsToggle}
            />
          </div>

          {isLoading ? (
            <SpendAnalysisLoader />
          ) : clinicList.length === 0 ? (
            <div className="py-8">
              <NoResults
                label={
                  filters.query
                    ? `No clinics found matching "${filters.query}"`
                    : filters.inactive_only
                      ? 'No inactive clinics found'
                      : 'No clinics found'
                }
              />
            </div>
          ) : (
            <>
              <div className="flex flex-col gap-2">
                {clinicList.map((clinicData) => (
                  <ClinicCard
                    key={clinicData.clinic.id}
                    clinic={clinicData.clinic}
                    spendAnalysis={clinicData.spendAnalysis}
                    isSelected={
                      selectedClinics.has(clinicData.clinic.id) || allSelected
                    }
                    onSelect={(checked: boolean) =>
                      handleSelectClinic(clinicData.clinic.id, checked)
                    }
                  />
                ))}
              </div>
            </>
          )}
        </div>
      </div>
      <Pagination
        itemsPerPage={data?.pagination.per_page.toString() || '12'}
        page={data?.pagination.current_page || 1}
        total={data?.pagination.total || 0}
        onPageChange={(page) => goToPage(page)}
        onChangeItemsPerPage={(perPage) =>
          updateFilters({
            per_page: +perPage,
          })
        }
        limitOptions={[
          {
            value: '12',
            label: '12',
          },
          {
            value: '24',
            label: '24',
          },
          {
            value: '36',
            label: '36',
          },
          {
            value: '48',
            label: '48',
          },
        ]}
      />
    </div>
  );
};
