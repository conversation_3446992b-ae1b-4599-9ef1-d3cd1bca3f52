import ReactDOM from 'react-dom/client';
import '@/libs/i18n';
import { initializeSentry } from '@/libs/sentry/utils/initializeSentry';
import { createSentryConfig } from '@/libs/sentry/utils/createSentryConfig';
import { GpoPortal } from './GpoPortal';

initializeSentry(createSentryConfig({ appName: 'gpo-portal' }));

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement,
);
root.render(<GpoPortal />);
