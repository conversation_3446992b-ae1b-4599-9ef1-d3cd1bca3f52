import { Loader } from '@/libs/ui/Loader/Loader';
import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import { SentryErrorBoundary } from '@/libs/sentry/components/SentryErrorBoundary';
import { ProtectedLayout } from '@/apps/gpo-portal/Layouts/ProtectedLayout/ProtectedLayout';
import { AppContentWrap } from '@/libs/ui/AppContentWrap/AppContentWrap';
import { GpoSidebar } from '../../components/GpoSidebar/GpoSidebar';
import { GpoTopNavbar } from '../../components/GpoTopNavbar/GpoTopNavbar';

export const DashboardLayout = () => {
  return (
    <SentryErrorBoundary>
      <Suspense
        fallback={
          <div className="loaderRoot height100vh">
            <Loader size="3rem" />
          </div>
        }
      >
        <ProtectedLayout>
          <AppContentWrap>
            <div className="relative flex">
              <GpoSidebar />
              <div className="flex-1">
                <GpoTopNavbar />
                <SentryErrorBoundary>
                  <Outlet />
                </SentryErrorBoundary>
              </div>
            </div>
          </AppContentWrap>
        </ProtectedLayout>
      </Suspense>
    </SentryErrorBoundary>
  );
};
