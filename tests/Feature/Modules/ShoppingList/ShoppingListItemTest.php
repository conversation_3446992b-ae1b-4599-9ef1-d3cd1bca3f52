<?php

declare(strict_types=1);

use App\Models\ProductOffer;
use App\Models\User;
use App\Modules\ShoppingList\Models\ClinicShoppingList;
use App\Modules\ShoppingList\Models\ShoppingListItem;
use Illuminate\Database\QueryException;

describe('ShoppingListItem', function () {
    it('can create a shopping list item', function () {
        $shoppingList = ClinicShoppingList::factory()->create();
        $productOffer = ProductOffer::factory()->create();
        $user = User::factory()->create();

        $item = ShoppingListItem::factory()->create([
            'clinic_shopping_list_id' => $shoppingList->id,
            'product_offer_id' => $productOffer->id,
            'created_by_user_id' => $user->id,
            'quantity' => 5,
            'color' => '#FF5733',
            'label' => 'Urgent',
            'rank' => 1,
        ]);

        expect($item)->toBeInstanceOf(ShoppingListItem::class)
            ->and($item->clinic_shopping_list_id)->toBe($shoppingList->id)
            ->and($item->product_offer_id)->toBe($productOffer->id)
            ->and($item->created_by_user_id)->toBe($user->id)
            ->and($item->quantity)->toBe(5)
            ->and($item->color)->toBe('#FF5733')
            ->and($item->label)->toBe('Urgent')
            ->and($item->rank)->toBe(1);
    });
});

describe('Constraints', function () {
    it('prevents duplicate shopping list and product offer combination', function () {
        $shoppingList = ClinicShoppingList::factory()->create();
        $productOffer = ProductOffer::factory()->create();

        ShoppingListItem::factory()->create([
            'clinic_shopping_list_id' => $shoppingList->id,
            'product_offer_id' => $productOffer->id,
        ]);

        expect(function () use ($shoppingList, $productOffer) {
            ShoppingListItem::factory()->create([
                'clinic_shopping_list_id' => $shoppingList->id,
                'product_offer_id' => $productOffer->id,
            ]);
        })->toThrow(QueryException::class);
    });

    it('allows same product offer in different shopping lists', function () {
        $shoppingList1 = ClinicShoppingList::factory()->create();
        $shoppingList2 = ClinicShoppingList::factory()->create();
        $productOffer = ProductOffer::factory()->create();

        $item1 = ShoppingListItem::factory()->create([
            'clinic_shopping_list_id' => $shoppingList1->id,
            'product_offer_id' => $productOffer->id,
        ]);

        $item2 = ShoppingListItem::factory()->create([
            'clinic_shopping_list_id' => $shoppingList2->id,
            'product_offer_id' => $productOffer->id,
        ]);

        expect($item1->id)->not->toBe($item2->id)
            ->and($item1->product_offer_id)->toBe($item2->product_offer_id);
    });
});
