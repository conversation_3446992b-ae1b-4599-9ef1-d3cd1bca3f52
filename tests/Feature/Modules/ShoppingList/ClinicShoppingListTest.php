<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Modules\ShoppingList\Models\ClinicShoppingList;
use App\Modules\ShoppingList\Models\ShoppingListItem;

describe('ClinicShoppingList', function () {
    it('can create a clinic shopping list', function () {
        $clinic = Clinic::factory()->create();
        $user = User::factory()->create();

        $shoppingList = ClinicShoppingList::factory()->create([
            'clinic_id' => $clinic->id,
            'created_by_user_id' => $user->id,
            'title' => 'Test Shopping List',
        ]);

        expect($shoppingList)->toBeInstanceOf(ClinicShoppingList::class)
            ->and($shoppingList->title)->toBe('Test Shopping List')
            ->and($shoppingList->clinic_id)->toBe($clinic->id)
            ->and($shoppingList->created_by_user_id)->toBe($user->id);
    });
});

describe('Relationships', function () {
    it('belongs to a clinic', function () {
        $shoppingList = ClinicShoppingList::factory()->create();

        expect($shoppingList->clinic)->toBeInstanceOf(Clinic::class)
            ->and($shoppingList->clinic->id)->toBe($shoppingList->clinic_id);
    });

    it('belongs to a user', function () {
        $shoppingList = ClinicShoppingList::factory()->create();

        expect($shoppingList->createdByUser)->toBeInstanceOf(User::class)
            ->and($shoppingList->createdByUser->id)->toBe($shoppingList->created_by_user_id);
    });

    it('can have many items', function () {
        $shoppingList = ClinicShoppingList::factory()->create();

        $item1 = ShoppingListItem::factory()->create(['clinic_shopping_list_id' => $shoppingList->id]);
        $item2 = ShoppingListItem::factory()->create(['clinic_shopping_list_id' => $shoppingList->id]);
        $item3 = ShoppingListItem::factory()->create(['clinic_shopping_list_id' => $shoppingList->id]);

        $shoppingList->refresh();

        expect($shoppingList->items)->toHaveCount(3)
            ->and($shoppingList->items->pluck('id'))->toContain($item1->id, $item2->id, $item3->id);
    });
});
