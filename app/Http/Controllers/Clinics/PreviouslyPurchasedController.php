<?php

declare(strict_types=1);

namespace App\Http\Controllers\Clinics;

use App\Http\Controllers\Controller;
use App\Modules\Order\Data\PreviouslyPurchasedItemData;
use App\Modules\Order\Queries\PreviouslyOrderedItemsQuery;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Spatie\LaravelData\PaginatedDataCollection;

final class PreviouslyPurchasedController extends Controller
{
    /**
     * Get previously purchased items for a clinic with pagination.
     */
    public function index(Request $request): JsonResponse
    {
        $paginatedResults = PreviouslyOrderedItemsQuery::for($request->clinicId())->jsonPaginate();
        $clinicId = $request->clinicId();

        // Load clinic once to avoid N+1 query problem
        $clinic = \App\Models\Clinic::with('account.gpo')->find($clinicId);

        // Transform directly from query result to PreviouslyPurchasedItemData in a single pass
        $dataCollection = $paginatedResults->getCollection()->map(function ($queryResult) use ($clinicId, $clinic) {
            return PreviouslyPurchasedItemData::fromQueryResult($queryResult, $clinicId, $clinic);
        });

        $paginatedResults->setCollection($dataCollection);

        $data = PreviouslyPurchasedItemData::collect(
            $paginatedResults,
            PaginatedDataCollection::class
        );

        return response()->json($data);
    }
}
