<?php

declare(strict_types=1);

namespace App\Modules\ShoppingList\Factories;

use App\Models\ProductOffer;
use App\Models\User;
use App\Modules\ShoppingList\Models\ClinicShoppingList;
use App\Modules\ShoppingList\Models\ShoppingListItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\ShoppingList\Models\ShoppingListItem>
 */
final class ShoppingListItemFactory extends Factory
{
    protected $model = ShoppingListItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'clinic_shopping_list_id' => ClinicShoppingList::factory(),
            'product_offer_id' => ProductOffer::factory(),
            'created_by_user_id' => User::factory(),
            'quantity' => fake()->numberBetween(1, 100),
            'color' => fake()->optional()->hexColor(),
            'label' => fake()->optional()->word(),
            'rank' => fake()->numberBetween(0, 100),
        ];
    }
}
