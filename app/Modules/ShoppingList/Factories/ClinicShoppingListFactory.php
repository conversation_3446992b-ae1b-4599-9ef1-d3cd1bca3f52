<?php

declare(strict_types=1);

namespace App\Modules\ShoppingList\Factories;

use App\Models\Clinic;
use App\Models\User;
use App\Modules\ShoppingList\Models\ClinicShoppingList;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\ShoppingList\Models\ClinicShoppingList>
 */
final class ClinicShoppingListFactory extends Factory
{
    protected $model = ClinicShoppingList::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'clinic_id' => Clinic::factory(),
            'created_by_user_id' => User::factory(),
            'title' => fake()->sentence(3),
        ];
    }
}
