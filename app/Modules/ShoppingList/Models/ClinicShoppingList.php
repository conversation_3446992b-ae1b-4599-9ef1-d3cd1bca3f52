<?php

declare(strict_types=1);

namespace App\Modules\ShoppingList\Models;

use App\Models\Clinic;
use App\Models\User;
use App\Modules\ShoppingList\Factories\ClinicShoppingListFactory;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class ClinicShoppingList extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    public function createdByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(ShoppingListItem::class);
    }

    protected static function newFactory(): ClinicShoppingListFactory
    {
        return ClinicShoppingListFactory::new();
    }
}
