<?php

declare(strict_types=1);

namespace App\Modules\ShoppingList\Models;

use App\Models\ProductOffer;
use App\Models\User;
use App\Modules\ShoppingList\Factories\ShoppingListItemFactory;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class ShoppingListItem extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    public function clinicShoppingList(): BelongsTo
    {
        return $this->belongsTo(ClinicShoppingList::class);
    }

    public function productOffer(): BelongsTo
    {
        return $this->belongsTo(ProductOffer::class);
    }

    public function createdByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    protected static function newFactory(): ShoppingListItemFactory
    {
        return ShoppingListItemFactory::new();
    }

    protected function casts(): array
    {
        return [
            'quantity' => 'integer',
            'rank' => 'integer',
        ];
    }
}
