import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import tsconfigPaths from 'vite-tsconfig-paths';
import { VitePWA } from 'vite-plugin-pwa';
import path from 'path';
import { getVersion } from './src/utils/version.node.js';

const APPS = ['shop', 'gpo-portal'];

export default defineConfig(({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
  const app = process.env.APP || APPS[0];

  // Get version using unified utility
  const version = getVersion();

  console.log(`Selected app: ${app}`);
  console.log(`App version: ${version}`);

  return {
    define: {
      __APP_VERSION__: JSON.stringify(version),
    },
    server: {
      port: 3000, // + APPS.indexOf(app),
      host: '0.0.0.0',
      open: true,
    },
    plugins: [
      react(),
      svgr(),
      tsconfigPaths(),
      VitePWA({
        registerType: 'prompt',
        includeAssets: ['favicon.svg', 'favicon.ico', 'apple-touch-icon.png'],
        manifest: {
          name: 'Highfive',
          short_name: 'Highfive',
          start_url: '/',
          display: 'standalone',
          background_color: '#FFFFFF',
          theme_color: '#FFFFFF',
          icons: [
            {
              src: 'web-app-manifest-192x192.png',
              sizes: '192x192',
              type: 'image/png',
              purpose: 'maskable',
            },
            {
              src: 'web-app-manifest-512x512.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'maskable',
            },
          ],
        },
        workbox: {
          cleanupOutdatedCaches: true,
          skipWaiting: true,
          clientsClaim: true,
          navigateFallback: 'index.html',
          navigateFallbackDenylist: [/^\/_/, /\/[^/?]+\.[^/]+$/],
          globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2,ttf}'],
          maximumFileSizeToCacheInBytes: 5 * 1024 * 1024,
          runtimeCaching: [
            {
              urlPattern: ({ request }) => request.destination === 'script',
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'scripts',
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 60 * 60 * 24 * 30,
                },
              },
            },
            {
              urlPattern: ({ request }) => request.destination === 'style',
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'styles',
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 60 * 60 * 24 * 30,
                },
              },
            },
            {
              urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
              handler: 'CacheFirst',
              options: {
                cacheName: 'images',
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24 * 30,
                },
              },
            },
          ],
        },
        devOptions: {
          enabled: false,
        },
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    root: path.resolve(__dirname, `src/apps/${app}`),
    base: '/',
    publicDir: path.resolve(__dirname, './public'),
    build: {
      outDir: path.resolve(__dirname, `build/${app}`),
      sourcemap: process.env.NODE_ENV === 'staging',
      emptyOutDir: true,
    },
  };
});
