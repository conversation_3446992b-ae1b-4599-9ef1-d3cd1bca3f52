# Reusable workflow for version setup
name: Version Setup

on:
  workflow_call:
    outputs:
      git-tag:
        description: 'Git tag for versioning'
        value: ${{ jobs.version.outputs.git-tag }}
      git-commit:
        description: 'Git commit hash for versioning'
        value: ${{ jobs.version.outputs.git-commit }}

jobs:
  version:
    runs-on: ubuntu-latest
    outputs:
      git-tag: ${{ steps.git-tag.outputs.tag }}
      git-commit: ${{ steps.git-commit.outputs.commit }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get Git tag
        id: git-tag
        run: |
          if [ "${{ github.event_name }}" = "release" ]; then
            echo "tag=${{ github.event.release.tag_name }}" >> $GITHUB_OUTPUT
          else
            # Try to get latest tag, fallback to commit SHA if no tags exist
            if TAG=$(git describe --tags --abbrev=0 2>/dev/null); then
              echo "tag=$TAG" >> $GITHUB_OUTPUT
            else
              # No tags found, use short commit SHA as fallback
              TAG=$(git rev-parse --short HEAD)
              echo "tag=$TAG" >> $GITHUB_OUTPUT
            fi
          fi

      - name: Get Git commit
        id: git-commit
        run: echo "commit=${{ github.sha }}" >> $GITHUB_OUTPUT
