name: Deploy Production

on:
  release:
    types:
      - published

jobs:
  version:
    uses: ./.github/workflows/version-setup.yml

  deploy:
    needs: version
    environment: production
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build Shop App with Sentry
        run: |
          VITE_GIT_TAG=${{ needs.version.outputs.git-tag }} VITE_GIT_COMMIT=${{ needs.version.outputs.git-commit }} npm run build:shop
        env:
          VITE_SENTRY_DSN: ${{ secrets.VITE_SENTRY_DSN }}
          VITE_ENVIRONMENT: production

      - name: Build GPO Portal App with Sentry
        run: |
          VITE_GIT_TAG=${{ needs.version.outputs.git-tag }} VITE_GIT_COMMIT=${{ needs.version.outputs.git-commit }} npm run build:gpo
        env:
          VITE_SENTRY_DSN: ${{ secrets.VITE_SENTRY_DSN }}
          VITE_ENVIRONMENT: production

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          tools: composer:v2
          coverage: none

      - name: Require Forge CLI
        run: composer global require laravel/forge-cli

      - name: Deploy Sites
        run: |
          forge server:switch highfive-backend-staging
          forge deploy demo.app.highfive.vet
          forge deploy demo.gpo.highfive.vet
          forge server:switch highfive-backend-production
          forge deploy app.highfive.vet
          forge deploy gpo.highfive.vet
        env:
          FORGE_API_TOKEN: ${{ secrets.FORGE_API_TOKEN }}
